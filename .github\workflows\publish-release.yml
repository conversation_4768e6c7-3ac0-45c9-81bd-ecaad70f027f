name: Publish Release (with Version & Release Check)

on:
  workflow_dispatch:
    inputs:
      confirm:
        description: 'Confirm release creation'
        required: true
        default: 'yes'
      normalize_tashkeel:
        description: 'Run tashkeel normalizer on all .md files before release'
        required: false
        default: 'yes'
        type: choice
        options:
          - 'yes'
          - 'no'

jobs:
  publish-release:
    runs-on: ubuntu-latest
    if: github.event.inputs.confirm == 'yes'
    permissions:
      contents: write # Needed to create releases and tags
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Important: Fetch all history to ensure tags are available

      - name: Get Latest GitHub Release Tag
        id: get_latest_release_tag
        run: |
          # Fetch the latest release from the GitHub API
          LATEST_RELEASE_TAG=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/releases/latest" | jq -r '.tag_name')

          if [ -z "$LATEST_RELEASE_TAG" ]; then
            echo "No previous releases found. Proceeding without latest release comparison."
            echo "LATEST_GITHUB_RELEASE_VERSION=" >> $GITHUB_ENV # Set to empty if no release
          else
            # Remove 'v' prefix if present for numerical comparison
            LATEST_RELEASE_VERSION_NUMERIC="${LATEST_RELEASE_TAG#v}"
            echo "Latest GitHub Release Version: $LATEST_RELEASE_TAG (Numeric: $LATEST_RELEASE_VERSION_NUMERIC)"
            echo "LATEST_GITHUB_RELEASE_VERSION=$LATEST_RELEASE_VERSION_NUMERIC" >> $GITHUB_ENV
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # Ensure token is available for this step

      - name: Check KMN File Versions
        id: check_kmn_versions
        run: |
          # Initialize an array to store extracted versions
          declare -a versions

          # Find all .kmn files and extract their versions
          find . -type f -name "*.kmn" | while read -r file; do
            VERSION=$(grep -oP "store\(&KEYBOARDVERSION\) '\K[^']+" "$file")
            if [ -z "$VERSION" ]; then
              echo "Error: Could not find version in '$file'."
              echo "Please ensure all .kmn files have 'store(&KEYBOARDVERSION) 'X.Y.Z'' defined."
              exit 1
            fi
            echo "Found version '$VERSION' in '$file'"
            versions+=("$VERSION")
          done

          # Check if any .kmn files were found
          if [ ${#versions[@]} -eq 0 ]; then
            echo "Error: No .kmn files found in the repository."
            exit 1
          fi

          # Check if all .kmn versions are identical
          FIRST_KMN_VERSION="${versions[0]}"
          ALL_KMN_IDENTICAL=true
          for v in "${versions[@]}"; do
            if [ "$v" != "$FIRST_KMN_VERSION" ]; then
              ALL_KMN_IDENTICAL=false
              break
            fi
          done

          if [ "$ALL_KMN_IDENTICAL" = false ]; then
            echo "::error::KMN version mismatch detected!"
            echo "::error::The versions in your .kmn files are not identical."
            echo "::error::Please make all of them have the same version number."
            echo "::error::"
            echo "::error::After standardizing the versions, ensure you build the source"
            echo "::error::then come back to this workflow and trigger it again."
            exit 1 # Stop the workflow
          else
            echo "All .kmn files have the same version: $FIRST_KMN_VERSION"
            # Set the common KMN version as an environment variable for subsequent steps
            echo "KMN_COMMON_VERSION=$FIRST_KMN_VERSION" >> $GITHUB_ENV
          fi
          
          # Compare KMN version with latest GitHub release version
          if [ -n "${{ env.LATEST_GITHUB_RELEASE_VERSION }}" ]; then # Only compare if a latest release was found
            echo "Comparing KMN version ($FIRST_KMN_VERSION) with Latest GitHub Release version (${{ env.LATEST_GITHUB_RELEASE_VERSION }})..."
            if [ "$FIRST_KMN_VERSION" == "${{ env.LATEST_GITHUB_RELEASE_VERSION }}" ]; then
              echo "::error::Current KMN version ($FIRST_KMN_VERSION) is identical to the latest GitHub release version!"
              echo "::error::Please increment the version(s) in your .kmn files."
              echo "::error::"
              echo "::error::Example: If current is '1.0.0', change to '1.0.1' or '1.1.0' or '2.0.0'."
              echo "::error::After incrementing, ensure you build the source (if necessary)"
              echo "::error::then come back to this workflow and trigger it again."
              exit 1 # Stop the workflow
            else
              echo "KMN version is different from the latest GitHub release version. Proceeding."
            fi
          fi

      # The rest of your workflow steps follow, now using KMN_COMMON_VERSION for 'VERSION'
      - name: Get Previous Release Tag
        id: get_previous_tag
        run: |
          # Use the LATEST_GITHUB_RELEASE_VERSION if available, otherwise fallback
          if [ -n "${{ env.LATEST_GITHUB_RELEASE_VERSION }}" ]; then
              PREVIOUS_TAG="v${{ env.LATEST_GITHUB_RELEASE_VERSION }}"
              echo "Using latest GitHub release tag for previous version: $PREVIOUS_TAG"
          else
              echo "No previous release found. Defaulting to 'v1.0.0' for changelog comparison."
              PREVIOUS_TAG="v1.0.0" # Fallback if no previous release exists
          fi
          
          # Sets the PREVIOUS_VERSION environment variable for subsequent steps.
          echo "PREVIOUS_VERSION=$PREVIOUS_TAG" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python for Tashkeel Normalizer
        if: github.event.inputs.normalize_tashkeel == 'yes'
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Normalize Tashkeel in Markdown Files
        if: github.event.inputs.normalize_tashkeel == 'yes'
        run: |
          echo "Running tashkeel normalizer on all .md files (excluding LICENSE files)..."

          # Find all .md files in the repository, excluding files with "LICENSE" in their name
          find . -name "*.md" -type f | grep -v -i "LICENSE" | while read -r file; do
            echo "Processing: $file"

            # Create a temporary Python script to process the file in batch mode
            python3 -c "
          import sys
          import os
          sys.path.append('source/tools')
          from tashkeel_normalizer import process_file, normalize_tashkeel

          file_path = '$file'
          try:
              with open(file_path, 'r', encoding='utf-8') as f:
                  content = f.read()

              normalized_content = normalize_tashkeel(content)

              with open(file_path, 'w', encoding='utf-8') as f:
                  f.write(normalized_content)

              print(f'Successfully normalized: {file_path}')
          except Exception as e:
              print(f'Error processing {file_path}: {str(e)}')
              sys.exit(1)
          "
          done

          echo "Tashkeel normalization completed for all .md files (LICENSE files excluded)."

      - name: Read Changelog for Current Version
        id: read_changelog
        run: |
          # Gets the full current version (e.g., '2.32.9') from the environment variable.
          CURRENT_VERSION_FULL="${{ env.KMN_COMMON_VERSION }}"

          # The exact heading to look for in CHANGELOG.md, e.g., "## 2.32.9".
          TARGET_CHANGELOG_HEADING_VERSION="${CURRENT_VERSION_FULL}"

          echo "Looking for changelog section: ## ${TARGET_CHANGELOG_HEADING_VERSION}"

          CHANGELOG_CONTENT=$(awk -v version_heading="## ${TARGET_CHANGELOG_HEADING_VERSION}" '
              $0 ~ version_heading {
                  p=1;
                  next
              }
              /^## / {
                  p=0
              }
              p
          ' source/Docs/CHANGELOG.md)

          if [ -z "$CHANGELOG_CONTENT" ]; then
            echo "Warning: No specific changelog section found for version ${{ env.KMN_COMMON_VERSION }} (looking for heading '## ${TARGET_CHANGELOG_HEADING_VERSION}')."
            CHANGELOG_CONTENT="No specific changelog found for this version. Please check 'source/Docs/CHANGELOG.md'."
          fi

          FULL_CHANGELOG_SECTION="## v${{ env.KMN_COMMON_VERSION }}\n\n$CHANGELOG_CONTENT"

          echo "CHANGELOG_CONTENT<<EOF" >> $GITHUB_ENV
          echo "$FULL_CHANGELOG_SECTION" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Read Download Guide
        id: read_download_guide
        run: |
          # Read the download guide content from the external file
          if [ -f "source/Docs/download-guide.md" ]; then
            DOWNLOAD_GUIDE_CONTENT=$(cat source/Docs/download-guide.md)
            echo "Successfully read download guide from source/Docs/download-guide.md"
          else
            echo "Warning: Download guide file not found at source/Docs/download-guide.md"
            DOWNLOAD_GUIDE_CONTENT="Download guide not available. Please check the repository."
          fi

          echo "DOWNLOAD_GUIDE_CONTENT<<EOF" >> $GITHUB_ENV
          echo "$DOWNLOAD_GUIDE_CONTENT" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ env.KMN_COMMON_VERSION }}
          name: v${{ env.KMN_COMMON_VERSION }}
          draft: false
          prerelease: false
          make_latest: true
          files: |
            build/Arabiyy.kmx
            build/ArabiyyNumOnShift.kmx
            build/arabiyymobile.kmp
          body: |
            ${{ env.CHANGELOG_CONTENT }}

            ---

            ${{ env.DOWNLOAD_GUIDE_CONTENT }}

            ---

            **Full Changelog**: [`${{ env.PREVIOUS_VERSION }}`](https://github.com/O1Anas/Arabiyy-keyboard/compare/${{ env.PREVIOUS_VERSION }}...v${{ env.KMN_COMMON_VERSION }})

        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}